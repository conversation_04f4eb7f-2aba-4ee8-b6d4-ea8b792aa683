// main.rs - Main Entry Point for Matrix AI Rust Backend
// This file integrates all backend modules and provides a command-line interface
// for processing input from the frontend.

use std::env;
use std::io;
// Module declarations - using file names as they exist
mod Automations;
mod Chatbot;
mod RealTimeSearch;
mod SpeechToText;
mod TextToSpeech;

fn main() {
    // Check for command-line arguments to process input directly (from frontend).
    let args: Vec<String> = env::args().collect();
    if args.len() > 1 {
        // Process the input provided as a command-line argument.
        let input = args[1..].join(" ");
        let response = process_input(&input);
        println!("{}", response);
        return;
    }

    // Otherwise, enter an interactive loop for testing.
    println!("Matrix AI Backend - Interactive Mode");
    println!("Enter input (or type 'exit' to quit):");
    
    loop {
        let mut input = String::new();
        io::stdin()
            .read_line(&mut input)
            .expect("Failed to read line");
        
        let input = input.trim();
        if input.eq_ignore_ascii_case("exit") {
            println!("Exiting Matrix AI Backend.");
            break;
        }
        
        let response = process_input(input);
        println!("Matrix AI: {}", response);
    }
}

fn process_input(input: &str) -> String {
    // Use the chatbot module to process input.
    match Chatbot::process_input(input) {
        Ok(response) => response,
        Err(e) => format!("Error processing input: {}", e),
    }
}

// Include other modules (assuming they are in separate files).
// In a real project, these would be properly organized with `mod.rs` files.
