# main.py - Entry Point for Matrix AI
# This script initializes the Matrix AI system, connects to the Rust backend,
# and launches the GUI for testing.

import sys
import os
import subprocess
from Frontend.GUI import MatrixAIG<PERSON>

def initialize_system():
    """Initialize the Matrix AI system components, including the Rust backend."""
    print("Initializing Matrix AI system...")
    
    # Placeholder for compiling or loading Rust backend.
    try:
        # Attempt to run a Rust executable or script if compiled.
        # This assumes the Rust code is compiled into an executable named 'matrix_backend'.
        backend_path = os.path.join(os.getcwd(), "Backend", "matrix_backend.exe")
        backend_bat_path = os.path.join(os.getcwd(), "Backend", "matrix_backend.bat")

        if os.path.exists(backend_path):
            subprocess.Popen([backend_path], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            print("Rust backend started successfully.")
        elif os.path.exists(backend_bat_path):
            print("Using mock backend for testing.")
            print("Mock backend is available for GUI interaction.")
        else:
            print("Backend executable not found. Please compile the backend first.")
    except Exception as e:
        print(f"Error starting backend: {e}")
        # Fallback to placeholder mode if backend connection fails.
        print("Continuing with placeholder backend functionality.")
    
    print("System initialization complete. Starting GUI...")

def main():
    """Main function to start the Matrix AI application."""
    # Ensure the working directory is set correctly.
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    # Initialize the system components.
    initialize_system()
    
    # Launch the GUI.
    app = MatrixAIGUI()
    app.run()

if __name__ == "__main__":
    main()
