// Chatbot.rs - Core Chatbot Logic Module for Matrix AI
// This module processes user input, manages conversation context,
// and integrates with the LLM for natural language understanding.

use std::fs::File;
use std::io::{Read, Write};
use std::error::Error;
use serde::{Deserialize, Serialize};
use serde_json;
use std::path::Path;

/// Struct to represent the AI's profile and memory.
#[derive(Serialize, Deserialize)]
struct MatrixProfile {
    ai_identity: AiIdentity,
    capabilities: Vec<String>,
    user_information: UserInformation,
    interaction_memory: Vec<Interaction>,
}

#[derive(Serialize, Deserialize)]
struct AiIdentity {
    name: String,
    role: String,
}

#[derive(Serialize, Deserialize)]
struct UserInformation {
    name: String,
}

#[derive(Serialize, Deserialize)]
struct Interaction {
    timestamp: String,
    user_input: String,
    ai_response: String,
}

/// Processes user input to determine intent and generate a response.
/// 
/// # Arguments
/// * `input` - A string slice containing the user's input text.
/// 
/// # Returns
/// * `Result<String, Box<dyn Error>>` - The generated response as a String, or an error.
pub fn process_input(input: &str) -> Result<String, Box<dyn Error>> {
    // Load the current profile and memory.
    let profile_path = "Backend/MatrixProfile.json";
    let mut profile = load_profile(profile_path)?;
    
    // Log the user input to memory.
    let interaction = Interaction {
        timestamp: current_timestamp(),
        user_input: input.to_string(),
        ai_response: "".to_string(), // Placeholder until response is generated.
    };
    profile.interaction_memory.push(interaction);
    
    // Simple intent detection (placeholder for more complex logic).
    let response = if input.to_lowercase().contains("open") || input.to_lowercase().contains("launch") {
        handle_automation_request(input)?
    } else {
        // Placeholder for LLM integration.
        format!("Matrix AI received: {}. Generating response...", input)
    };
    
    // Update the interaction memory with the response.
    if let Some(last_interaction) = profile.interaction_memory.last_mut() {
        last_interaction.ai_response = response.clone();
    }
    
    // Save the updated profile.
    save_profile(&profile, profile_path)?;
    
    Ok(response)
}

/// Loads the AI profile and memory from a JSON file.
/// 
/// # Arguments
/// * `path` - A string slice representing the file path to the profile JSON.
/// 
/// # Returns
/// * `Result<MatrixProfile, Box<dyn Error>>` - The loaded profile, or an error.
fn load_profile(path: &str) -> Result<MatrixProfile, Box<dyn Error>> {
    let path = Path::new(path);
    if !path.exists() {
        return Err("Profile file does not exist".into());
    }
    
    let mut file = File::open(path)?;
    let mut contents = String::new();
    file.read_to_string(&mut contents)?;
    
    let profile: MatrixProfile = serde_json::from_str(&contents)?;
    Ok(profile)
}

/// Saves the AI profile and memory to a JSON file.
/// 
/// # Arguments
/// * `profile` - The MatrixProfile struct to save.
/// * `path` - A string slice representing the file path to save the profile JSON.
/// 
/// # Returns
/// * `Result<(), Box<dyn Error>>` - Ok if saved successfully, Err otherwise.
fn save_profile(profile: &MatrixProfile, path: &str) -> Result<(), Box<dyn Error>> {
    let json = serde_json::to_string_pretty(profile)?;
    let mut file = File::create(path)?;
    file.write_all(json.as_bytes())?;
    Ok(())
}

/// Handles automation requests extracted from user input.
/// 
/// # Arguments
/// * `input` - A string slice containing the user's input text.
/// 
/// # Returns
/// * `Result<String, Box<dyn Error>>` - The response to the automation request, or an error.
fn handle_automation_request(input: &str) -> Result<String, Box<dyn Error>> {
    // Placeholder: Would call functions from Automations.rs.
    Ok(format!("Matrix AI is handling automation request: {}", input))
}

/// Gets the current timestamp as a string (placeholder implementation).
/// 
/// # Returns
/// * `String` - The current timestamp.
fn current_timestamp() -> String {
    // Placeholder: Actual implementation would use a library like `chrono`.
    "2025-07-08 13:30:00".to_string()
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_process_input() {
        let input = "Hello, Matrix";
        let result = process_input(input);
        assert!(result.is_ok());
        if let Ok(response) = result {
            assert!(response.contains("Matrix AI received"));
        }
    }

    #[test]
    fn test_process_automation_input() {
        let input = "Open Notepad";
        let result = process_input(input);
        assert!(result.is_ok());
        if let Ok(response) = result {
            assert!(response.contains("automation request"));
        }
    }
}
