# Requirements for Matrix AI Python components
# These dependencies are needed for the GUI and integration with Rust backend

# GUI dependencies
tkinter>=8.6  # Built-in with Python, listed for clarity
pyaudio>=0.2.11  # For audio input/output handling

# Rust integration (if using Python-Rust bindings)
pyo3>=0.20.0  # For potential Rust-Python interoperability

# Other utilities
json>=2.0.9  # Built-in, for handling JSON data like MatrixProfile
datetime>=4.3  # Built-in, for timestamp handling
