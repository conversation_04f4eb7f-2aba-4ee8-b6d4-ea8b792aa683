#!/usr/bin/env python3
"""
Test suite for Matrix AI GUI components
Tests the GUI functionality, backend integration, and user interactions
"""

import unittest
import sys
import os
import tkinter as tk
from unittest.mock import Mock, patch, MagicMock
import subprocess

# Add the project root to the path so we can import modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from Frontend.GUI import MatrixAIGUI


class TestMatrixAIGUI(unittest.TestCase):
    """Test cases for the Matrix AI GUI"""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        self.root = tk.Tk()
        self.root.withdraw()  # Hide the window during testing
        
    def tearDown(self):
        """Clean up after each test method."""
        if self.root:
            self.root.destroy()
    
    def test_gui_initialization(self):
        """Test that the GUI initializes correctly"""
        app = MatrixAIGUI()
        
        # Check that main components are created
        self.assertIsNotNone(app.root)
        self.assertIsNotNone(app.conversation)
        self.assertIsNotNone(app.input_entry)
        self.assertIsNotNone(app.send_button)
        self.assertIsNotNone(app.listen_button)
        self.assertIsNotNone(app.stop_button)
        
        # Check initial state
        self.assertFalse(app.is_listening)
        self.assertIsNone(app.backend_process)
        
        app.root.destroy()
    
    def test_send_input_functionality(self):
        """Test the send input functionality"""
        app = MatrixAIGUI()
        
        # Mock the process_input method to avoid backend dependency
        with patch.object(app, 'process_input', return_value="Test response"):
            # Set some input text
            app.input_entry.insert(0, "Hello Matrix")
            
            # Call send_input
            app.send_input()
            
            # Check that input field is cleared
            self.assertEqual(app.input_entry.get(), "")
        
        app.root.destroy()
    
    def test_listening_state_management(self):
        """Test the voice listening state management"""
        app = MatrixAIGUI()
        
        # Test start listening
        app.start_listening()
        self.assertTrue(app.is_listening)
        self.assertEqual(str(app.listen_button['state']), 'disabled')
        self.assertEqual(str(app.stop_button['state']), 'normal')
        self.assertEqual(str(app.input_entry['state']), 'disabled')

        # Test stop listening
        app.stop_listening()
        self.assertFalse(app.is_listening)
        self.assertEqual(str(app.listen_button['state']), 'normal')
        self.assertEqual(str(app.stop_button['state']), 'disabled')
        self.assertEqual(str(app.input_entry['state']), 'normal')
        
        app.root.destroy()
    
    @patch('subprocess.run')
    def test_process_input_with_backend(self, mock_subprocess):
        """Test process_input when backend is available"""
        app = MatrixAIGUI()
        
        # Mock successful backend response
        mock_result = Mock()
        mock_result.returncode = 0
        mock_result.stdout = "Backend response"
        mock_result.stderr = ""
        mock_subprocess.return_value = mock_result
        
        # Mock os.path.exists to return True (backend exists)
        with patch('os.path.exists', return_value=True):
            response = app.process_input("test input")
            self.assertEqual(response, "Backend response")
        
        app.root.destroy()
    
    @patch('subprocess.run')
    def test_process_input_backend_error(self, mock_subprocess):
        """Test process_input when backend returns an error"""
        app = MatrixAIGUI()
        
        # Mock backend error response
        mock_result = Mock()
        mock_result.returncode = 1
        mock_result.stdout = ""
        mock_result.stderr = "Backend error"
        mock_subprocess.return_value = mock_result
        
        # Mock os.path.exists to return True (backend exists)
        with patch('os.path.exists', return_value=True):
            response = app.process_input("test input")
            self.assertIn("Error from backend", response)
        
        app.root.destroy()
    
    def test_process_input_no_backend(self):
        """Test process_input when backend is not available"""
        app = MatrixAIGUI()
        
        # Mock os.path.exists to return False (no backend)
        with patch('os.path.exists', return_value=False):
            response = app.process_input("test input")
            self.assertIn("placeholder response", response)
            self.assertIn("test input", response)
        
        app.root.destroy()
    
    @patch('subprocess.run')
    def test_process_input_exception_handling(self, mock_subprocess):
        """Test process_input exception handling"""
        app = MatrixAIGUI()
        
        # Mock subprocess to raise an exception
        mock_subprocess.side_effect = subprocess.TimeoutExpired("cmd", 10)
        
        # Mock os.path.exists to return True (backend exists)
        with patch('os.path.exists', return_value=True):
            response = app.process_input("test input")
            self.assertIn("Error connecting to backend", response)
        
        app.root.destroy()


class TestIntegration(unittest.TestCase):
    """Integration tests for the Matrix AI system"""
    
    def test_main_module_import(self):
        """Test that the main module can be imported"""
        try:
            import main
            self.assertTrue(hasattr(main, 'main'))
            self.assertTrue(hasattr(main, 'initialize_system'))
        except ImportError as e:
            self.fail(f"Failed to import main module: {e}")
    
    def test_gui_module_import(self):
        """Test that the GUI module can be imported"""
        try:
            from Frontend.GUI import MatrixAIGUI
            self.assertTrue(callable(MatrixAIGUI))
        except ImportError as e:
            self.fail(f"Failed to import GUI module: {e}")
    
    def test_requirements_dependencies(self):
        """Test that required dependencies are available"""
        try:
            import tkinter
            import json
            import datetime
            # pyaudio might not be available in all test environments
            try:
                import pyaudio
            except ImportError:
                print("Warning: pyaudio not available (this is okay for basic testing)")
        except ImportError as e:
            self.fail(f"Required dependency not available: {e}")


if __name__ == '__main__':
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_suite.addTest(unittest.makeSuite(TestMatrixAIGUI))
    test_suite.addTest(unittest.makeSuite(TestIntegration))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Exit with appropriate code
    sys.exit(0 if result.wasSuccessful() else 1)
