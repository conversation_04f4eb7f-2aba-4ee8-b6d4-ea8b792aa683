#!/usr/bin/env python3
"""
Mock backend for Matrix AI testing
This simulates the Rust backend functionality for testing purposes
"""

import sys
import json
import os
from datetime import datetime


class MockMatrixBackend:
    """Mock implementation of the Matrix AI backend"""
    
    def __init__(self):
        self.profile_path = os.path.join("Backend", "MatrixProfile.json")
        self.load_profile()
    
    def load_profile(self):
        """Load the Matrix profile from JSON file"""
        try:
            if os.path.exists(self.profile_path):
                with open(self.profile_path, 'r') as f:
                    self.profile = json.load(f)
            else:
                # Create default profile
                self.profile = {
                    "ai_identity": {
                        "name": "Matrix",
                        "role": "AI assistant to provide assistance to the user"
                    },
                    "capabilities": [
                        "System automation",
                        "Natural language understanding",
                        "Speech-to-text conversion",
                        "Text-to-speech conversion",
                        "Real-time content reading",
                        "Context-aware responses"
                    ],
                    "user_information": {
                        "name": ""
                    },
                    "interaction_memory": []
                }
        except Exception as e:
            print(f"Error loading profile: {e}")
            self.profile = {"interaction_memory": []}
    
    def save_profile(self):
        """Save the Matrix profile to JSON file"""
        try:
            os.makedirs(os.path.dirname(self.profile_path), exist_ok=True)
            with open(self.profile_path, 'w') as f:
                json.dump(self.profile, f, indent=2)
        except Exception as e:
            print(f"Error saving profile: {e}")
    
    def process_input(self, user_input):
        """Process user input and generate a response"""
        # Log the interaction
        interaction = {
            "timestamp": datetime.now().isoformat(),
            "user_input": user_input,
            "ai_response": ""
        }
        
        # Simple intent detection and response generation
        user_input_lower = user_input.lower()
        
        if any(word in user_input_lower for word in ["hello", "hi", "hey"]):
            response = f"Hello! I'm Matrix, your AI assistant. How can I help you today?"
        
        elif any(word in user_input_lower for word in ["open", "launch", "start"]):
            if "notepad" in user_input_lower:
                response = "I would open Notepad for you. (Mock response - automation not implemented)"
            elif "calculator" in user_input_lower:
                response = "I would open Calculator for you. (Mock response - automation not implemented)"
            else:
                response = f"I would help you open or launch something. What specifically would you like me to open?"
        
        elif any(word in user_input_lower for word in ["time", "date"]):
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            response = f"The current date and time is: {current_time}"
        
        elif any(word in user_input_lower for word in ["weather"]):
            response = "I would check the weather for you, but I'm currently running in offline mode. (Mock response)"
        
        elif any(word in user_input_lower for word in ["help", "what can you do"]):
            capabilities = self.profile.get("capabilities", [])
            response = f"I can help you with: {', '.join(capabilities)}. What would you like me to do?"
        
        elif any(word in user_input_lower for word in ["exit", "quit", "goodbye", "bye"]):
            response = "Goodbye! It was nice talking with you."
        
        elif "test" in user_input_lower:
            response = "Test successful! The mock backend is working correctly."
        
        else:
            response = f"I received your message: '{user_input}'. I'm a mock backend, so I can provide basic responses. How can I assist you further?"
        
        # Update interaction with response
        interaction["ai_response"] = response
        
        # Add to memory
        if "interaction_memory" not in self.profile:
            self.profile["interaction_memory"] = []
        self.profile["interaction_memory"].append(interaction)
        
        # Keep only last 10 interactions to prevent file from growing too large
        if len(self.profile["interaction_memory"]) > 10:
            self.profile["interaction_memory"] = self.profile["interaction_memory"][-10:]
        
        # Save updated profile
        self.save_profile()
        
        return response


def main():
    """Main function for command-line usage"""
    backend = MockMatrixBackend()
    
    if len(sys.argv) > 1:
        # Process command-line input
        user_input = " ".join(sys.argv[1:])
        response = backend.process_input(user_input)
        print(response)
    else:
        # Interactive mode
        print("Matrix AI Mock Backend - Interactive Mode")
        print("Enter input (or type 'exit' to quit):")
        
        while True:
            try:
                user_input = input("> ").strip()
                if user_input.lower() in ['exit', 'quit']:
                    print("Exiting Matrix AI Mock Backend.")
                    break
                
                if user_input:
                    response = backend.process_input(user_input)
                    print(f"Matrix AI: {response}")
                
            except KeyboardInterrupt:
                print("\nExiting Matrix AI Mock Backend.")
                break
            except EOFError:
                print("\nExiting Matrix AI Mock Backend.")
                break


if __name__ == "__main__":
    main()
