# GUI.py - Basic GUI for Matrix AI Testing
# This module provides a simple graphical user interface using Tkinter
# for interacting with the Matrix AI system.

import tkinter as tk
from tkinter import ttk, scrolledtext
import os
import subprocess
import json

class MatrixAIGUI:
    def __init__(self):
        """Initialize the Matrix AI GUI."""
        self.root = tk.Tk()
        self.root.title("Matrix AI Assistant")
        self.root.geometry("600x400")
        self.root.configure(bg="#1a1a1a")  # Dark theme background
        
        # Style configuration for dark theme
        style = ttk.Style()
        style.configure("TButton", background="#333333", foreground="#ffffff")
        style.configure("TLabel", background="#1a1a1a", foreground="#ffffff")
        style.configure("TEntry", fieldbackground="#333333", foreground="#ffffff")
        
        # Header
        self.header = ttk.Label(self.root, text="Matrix AI Assistant", font=("Arial", 16, "bold"))
        self.header.pack(pady=10)
        
        # Status display
        self.status_label = ttk.Label(self.root, text="Status: Ready", font=("Arial", 10))
        self.status_label.pack(pady=5)
        
        # Conversation display
        self.conversation = scrolledtext.ScrolledText(self.root, wrap=tk.WORD, height=15, width=60, bg="#2a2a2a", fg="#ffffff", insertbackground="#ffffff")
        self.conversation.pack(pady=10, padx=10, fill=tk.BOTH, expand=True)
        self.conversation.insert(tk.END, "Matrix AI: Hello! How can I assist you today?\n")
        self.conversation.configure(state='disabled')
        
        # Input field
        self.input_frame = ttk.Frame(self.root)
        self.input_frame.pack(pady=5, padx=10, fill=tk.X)
        
        self.input_label = ttk.Label(self.input_frame, text="Your Input:")
        self.input_label.pack(side=tk.LEFT)
        
        self.input_entry = ttk.Entry(self.input_frame, width=50)
        self.input_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        self.input_entry.bind("<Return>", self.send_input)
        
        # Buttons
        self.button_frame = ttk.Frame(self.root)
        self.button_frame.pack(pady=5)
        
        self.send_button = ttk.Button(self.button_frame, text="Send", command=self.send_input)
        self.send_button.pack(side=tk.LEFT, padx=5)
        
        self.listen_button = ttk.Button(self.button_frame, text="Start Listening", command=self.start_listening)
        self.listen_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = ttk.Button(self.button_frame, text="Stop Listening", command=self.stop_listening, state='disabled')
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        # Initialize state variables
        self.is_listening = False
        self.backend_process = None

    def send_input(self, event=None):
        """Handle user input from the entry field."""
        user_input = self.input_entry.get()
        if user_input.strip():
            # Display user input in conversation
            self.conversation.configure(state='normal')
            self.conversation.insert(tk.END, f"You: {user_input}\n")
            self.conversation.configure(state='disabled')
            self.input_entry.delete(0, tk.END)
            
            # Process input through backend
            self.status_label.config(text="Status: Processing...")
            self.root.update()
            
            # Attempt to get response from backend
            response = self.process_input(user_input)
            
            # Display response
            self.conversation.configure(state='normal')
            self.conversation.insert(tk.END, f"Matrix AI: {response}\n")
            self.conversation.see(tk.END)
            self.conversation.configure(state='disabled')
            self.status_label.config(text="Status: Ready")

    def start_listening(self):
        """Start listening for voice input (placeholder for actual implementation)."""
        self.is_listening = True
        self.status_label.config(text="Status: Listening...")
        self.listen_button.config(state='disabled')
        self.stop_button.config(state='normal')
        self.input_entry.config(state='disabled')
        print("Starting voice listening... (placeholder)")
        # Placeholder: Would trigger speech-to-text backend functionality.
        # Simulate voice input processing after a delay (for demo purposes)
        self.root.after(3000, self.simulate_voice_input)

    def stop_listening(self):
        """Stop listening for voice input."""
        self.is_listening = False
        self.status_label.config(text="Status: Ready")
        self.listen_button.config(state='normal')
        self.stop_button.config(state='disabled')
        self.input_entry.config(state='normal')
        print("Stopping voice listening... (placeholder)")

    def simulate_voice_input(self):
        """Simulate receiving voice input for testing purposes."""
        if self.is_listening:
            voice_input = "Hello Matrix, can you help me?"
            self.conversation.configure(state='normal')
            self.conversation.insert(tk.END, f"You (Voice): {voice_input}\n")
            self.conversation.configure(state='disabled')
            self.status_label.config(text="Status: Processing...")
            self.root.update()
            
            response = self.process_input(voice_input)
            
            self.conversation.configure(state='normal')
            self.conversation.insert(tk.END, f"Matrix AI: {response}\n")
            self.conversation.see(tk.END)
            self.conversation.configure(state='disabled')
            self.status_label.config(text="Status: Listening...")
            self.root.after(3000, self.simulate_voice_input)  # Continue listening simulation

    def process_input(self, user_input):
        """Process user input through the backend (attempt to connect to Rust backend)."""
        try:
            # Check if a backend executable or script exists to process input.
            backend_exe = os.path.join(os.getcwd(), "Backend", "matrix_backend.exe")
            backend_bat = os.path.join(os.getcwd(), "Backend", "matrix_backend.bat")

            backend_script = None
            if os.path.exists(backend_exe):
                backend_script = backend_exe
            elif os.path.exists(backend_bat):
                backend_script = backend_bat

            if backend_script:
                # Attempt to send input to backend process via subprocess.
                process = subprocess.run([backend_script, user_input], capture_output=True, text=True, timeout=10)
                if process.returncode == 0:
                    return process.stdout.strip() or "Backend processed the input but returned no response."
                else:
                    return f"Error from backend: {process.stderr}"
            else:
                # Fallback to placeholder response if backend is not available.
                return f"I received your input: '{user_input}'. How can I help further? (Backend not connected, using placeholder response.)"
        except Exception as e:
            return f"Error connecting to backend: {str(e)}. Using placeholder response for: '{user_input}'."

    def run(self):
        """Run the GUI main loop."""
        self.root.mainloop()

if __name__ == "__main__":
    app = MatrixAIGUI()
    app.run()
