#!/usr/bin/env python3
"""
Integration tests for Matrix AI system
Tests the full system integration between frontend and backend
"""

import unittest
import subprocess
import os
import sys
import time
import json

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))


class TestBackendIntegration(unittest.TestCase):
    """Test backend integration and communication"""
    
    def setUp(self):
        """Set up test environment"""
        self.project_root = os.path.join(os.path.dirname(__file__), '..')
        self.backend_bat = os.path.join(self.project_root, "Backend", "matrix_backend.bat")
        self.mock_backend = os.path.join(self.project_root, "Test", "mock_backend.py")
        
    def test_mock_backend_direct(self):
        """Test the mock backend directly"""
        result = subprocess.run([
            "python", self.mock_backend, "Hello Matrix"
        ], capture_output=True, text=True, cwd=self.project_root)
        
        self.assertEqual(result.returncode, 0)
        self.assertIn("Hello! I'm <PERSON>", result.stdout)
    
    def test_backend_batch_wrapper(self):
        """Test the backend through the batch wrapper"""
        if not os.path.exists(self.backend_bat):
            self.skipTest("Backend batch file not found")
        
        result = subprocess.run([
            self.backend_bat, "test"
        ], capture_output=True, text=True, cwd=self.project_root)
        
        self.assertEqual(result.returncode, 0)
        self.assertIn("Test successful", result.stdout)
    
    def test_backend_automation_commands(self):
        """Test backend automation command processing"""
        test_cases = [
            ("open notepad", "Notepad"),
            ("open calculator", "Calculator"),
            ("what time is it", "current date and time"),
            ("help", "can help you with"),
        ]
        
        for command, expected_response in test_cases:
            with self.subTest(command=command):
                result = subprocess.run([
                    "python", self.mock_backend, command
                ], capture_output=True, text=True, cwd=self.project_root)
                
                self.assertEqual(result.returncode, 0)
                self.assertIn(expected_response, result.stdout)
    
    def test_profile_persistence(self):
        """Test that the backend persists interaction history"""
        profile_path = os.path.join(self.project_root, "Backend", "MatrixProfile.json")
        
        # Clear any existing profile
        if os.path.exists(profile_path):
            os.remove(profile_path)
        
        # Send a test command
        subprocess.run([
            "python", self.mock_backend, "test interaction"
        ], capture_output=True, text=True, cwd=self.project_root)
        
        # Check that profile was created and contains the interaction
        self.assertTrue(os.path.exists(profile_path))
        
        with open(profile_path, 'r') as f:
            profile = json.load(f)
        
        self.assertIn("interaction_memory", profile)
        self.assertGreater(len(profile["interaction_memory"]), 0)
        self.assertEqual(profile["interaction_memory"][-1]["user_input"], "test interaction")


class TestGUIIntegration(unittest.TestCase):
    """Test GUI integration with backend"""
    
    def setUp(self):
        """Set up test environment"""
        self.project_root = os.path.join(os.path.dirname(__file__), '..')
        
    def test_gui_backend_communication(self):
        """Test that GUI can communicate with backend"""
        # Import GUI module
        from Frontend.GUI import MatrixAIGUI
        
        # Create GUI instance (without showing window)
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # Hide window
        
        try:
            app = MatrixAIGUI()
            app.root.withdraw()  # Hide this window too
            
            # Test backend communication
            response = app.process_input("test")
            
            # Should get a response from the mock backend
            self.assertIsInstance(response, str)
            self.assertGreater(len(response), 0)
            
            # Test specific responses
            hello_response = app.process_input("hello")
            self.assertIn("Matrix", hello_response)
            
            app.root.destroy()
            
        finally:
            root.destroy()


class TestSystemRequirements(unittest.TestCase):
    """Test system requirements and dependencies"""
    
    def test_python_version(self):
        """Test Python version compatibility"""
        version = sys.version_info
        self.assertGreaterEqual(version.major, 3)
        self.assertGreaterEqual(version.minor, 6)
    
    def test_required_modules(self):
        """Test that all required modules can be imported"""
        required_modules = [
            'tkinter',
            'json',
            'os',
            'subprocess',
            'datetime',
            'sys'
        ]
        
        for module_name in required_modules:
            with self.subTest(module=module_name):
                try:
                    __import__(module_name)
                except ImportError:
                    self.fail(f"Required module {module_name} not available")
    
    def test_project_structure(self):
        """Test that project structure is correct"""
        project_root = os.path.join(os.path.dirname(__file__), '..')
        
        required_files = [
            "main.py",
            "Requirements.txt",
            "README.md",
            "Frontend/GUI.py",
            "Backend/Cargo.toml",
            "Backend/main.rs",
            "Backend/MatrixProfile.json",
        ]
        
        for file_path in required_files:
            with self.subTest(file=file_path):
                full_path = os.path.join(project_root, file_path)
                self.assertTrue(os.path.exists(full_path), f"Required file {file_path} not found")


def run_integration_tests():
    """Run all integration tests"""
    print("Running Matrix AI Integration Tests...")
    print("=" * 50)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_suite.addTest(unittest.makeSuite(TestBackendIntegration))
    test_suite.addTest(unittest.makeSuite(TestGUIIntegration))
    test_suite.addTest(unittest.makeSuite(TestSystemRequirements))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    print("\n" + "=" * 50)
    if result.wasSuccessful():
        print("✅ All integration tests passed!")
        print("The Matrix AI system is ready to use.")
    else:
        print("❌ Some tests failed.")
        print(f"Failures: {len(result.failures)}")
        print(f"Errors: {len(result.errors)}")
    
    return result.wasSuccessful()


if __name__ == '__main__':
    success = run_integration_tests()
    sys.exit(0 if success else 1)
